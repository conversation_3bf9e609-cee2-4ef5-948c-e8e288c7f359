"""
闭环逆运动学(CLIK)算法实现
包含雅可比矩阵零空间处理和可操作性极限保护
"""

import numpy as np
import pinocchio as pin
from scipy.linalg import pinv, svd
from typing import Optional, Tuple
import warnings


class CLIKSolver:
    """
    闭环逆运动学求解器
    
    特点：
    1. 使用雅可比矩阵伪逆进行逆运动学求解
    2. 在接近奇异点时使用阻尼最小二乘法
    3. 利用零空间进行次要任务优化
    4. 可操作性指标监控和保护
    """
    
    def __init__(self, 
                 model: pin.Model, 
                 data: pin.Data,
                 end_effector_frame_id: int,
                 dt: float = 0.001,
                 lambda_damping: float = 0.01,
                 manipulability_threshold: float = 0.01,
                 max_joint_velocity: float = 2.0):
        """
        初始化CLIK求解器
        
        Args:
            model: Pinocchio机器人模型
            data: Pinocchio数据结构
            end_effector_frame_id: 末端执行器frame ID
            dt: 时间步长
            lambda_damping: 阻尼系数
            manipulability_threshold: 可操作性阈值
            max_joint_velocity: 最大关节速度限制
        """
        self.model = model
        self.data = data
        self.ee_frame_id = end_effector_frame_id
        self.dt = dt
        self.lambda_damping = lambda_damping
        self.manipulability_threshold = manipulability_threshold
        self.max_joint_velocity = max_joint_velocity
        
        self.nq = model.nq  # 关节数量
        self.nv = model.nv  # 速度维度
        
        # 历史数据用于平滑
        self.prev_q = None
        self.prev_error = None
        
    def compute_jacobian(self, q: np.ndarray) -> np.ndarray:
        """计算末端执行器雅可比矩阵"""
        pin.forwardKinematics(self.model, self.data, q)
        pin.updateFramePlacements(self.model, self.data)
        
        # 计算几何雅可比矩阵 (6x7 for 7-DOF arm)
        J = pin.computeFrameJacobian(
            self.model, self.data, q, self.ee_frame_id, pin.LOCAL_WORLD_ALIGNED
        )
        return J
    
    def compute_manipulability(self, J: np.ndarray) -> float:
        """
        计算可操作性指标
        
        Args:
            J: 雅可比矩阵
            
        Returns:
            可操作性指标 (Yoshikawa manipulability measure)
        """
        if J.shape[0] <= J.shape[1]:  # 冗余机械臂
            manipulability = np.sqrt(np.linalg.det(J @ J.T))
        else:  # 欠驱动情况
            manipulability = np.sqrt(np.linalg.det(J.T @ J))
        return manipulability
    
    def damped_least_squares_inverse(self, J: np.ndarray, lambda_damping: Optional[float] = None) -> np.ndarray:
        """
        阻尼最小二乘法求雅可比矩阵伪逆
        
        Args:
            J: 雅可比矩阵
            lambda_damping: 阻尼系数，如果为None则使用自适应阻尼
            
        Returns:
            阻尼伪逆矩阵
        """
        if lambda_damping is None:
            # 自适应阻尼：根据可操作性调整阻尼系数
            manipulability = self.compute_manipulability(J)
            if manipulability < self.manipulability_threshold:
                lambda_damping = self.lambda_damping * (1.0 - manipulability / self.manipulability_threshold)
            else:
                lambda_damping = 0.0
        
        m, n = J.shape
        if m <= n:  # 冗余情况
            # J_pinv = J.T @ inv(J @ J.T + λ²I)
            JJT_damped = J @ J.T + lambda_damping**2 * np.eye(m)
            J_pinv = J.T @ np.linalg.inv(JJT_damped)
        else:  # 欠驱动情况
            # J_pinv = inv(J.T @ J + λ²I) @ J.T
            JTJ_damped = J.T @ J + lambda_damping**2 * np.eye(n)
            J_pinv = np.linalg.inv(JTJ_damped) @ J.T
            
        return J_pinv
    
    def compute_nullspace_projector(self, J: np.ndarray, J_pinv: np.ndarray) -> np.ndarray:
        """
        计算雅可比矩阵的零空间投影矩阵
        
        Args:
            J: 雅可比矩阵
            J_pinv: 雅可比矩阵伪逆
            
        Returns:
            零空间投影矩阵 N = I - J_pinv @ J
        """
        I = np.eye(self.nv)
        N = I - J_pinv @ J
        return N
    
    def secondary_task_velocity(self, q: np.ndarray) -> np.ndarray:
        """
        次要任务：远离关节限位或优化可操作性
        
        Args:
            q: 当前关节角度
            
        Returns:
            次要任务关节速度
        """
        q_dot_secondary = np.zeros(self.nv)
        
        # 1. 远离关节限位
        for i in range(self.nq):
            q_min = self.model.lowerPositionLimit[i]
            q_max = self.model.upperPositionLimit[i]
            q_mid = (q_min + q_max) / 2.0
            
            # 如果接近限位，产生远离限位的速度
            if q[i] < q_min + 0.1:
                q_dot_secondary[i] += 0.5 * (q_mid - q[i])
            elif q[i] > q_max - 0.1:
                q_dot_secondary[i] += 0.5 * (q_mid - q[i])
        
        # 2. 可操作性优化（梯度上升）
        J = self.compute_jacobian(q)
        manipulability = self.compute_manipulability(J)
        
        if manipulability < self.manipulability_threshold:
            # 计算可操作性梯度（数值微分）
            epsilon = 1e-6
            grad_manipulability = np.zeros(self.nv)
            
            for i in range(self.nv):
                q_plus = q.copy()
                q_plus[i] += epsilon
                J_plus = self.compute_jacobian(q_plus)
                manipulability_plus = self.compute_manipulability(J_plus)
                
                q_minus = q.copy()
                q_minus[i] -= epsilon
                J_minus = self.compute_jacobian(q_minus)
                manipulability_minus = self.compute_manipulability(J_minus)
                
                grad_manipulability[i] = (manipulability_plus - manipulability_minus) / (2 * epsilon)
            
            # 添加可操作性优化项
            q_dot_secondary += 0.1 * grad_manipulability
        
        return q_dot_secondary

    def solve_ik(self,
                 q_current: np.ndarray,
                 target_pose: np.ndarray,
                 target_velocity: Optional[np.ndarray] = None,
                 use_nullspace_optimization: bool = True) -> Tuple[np.ndarray, dict]:
        """
        CLIK主求解函数

        Args:
            q_current: 当前关节角度
            target_pose: 目标末端执行器位姿 [x, y, z, qx, qy, qz, qw]
            target_velocity: 目标末端执行器速度 [vx, vy, vz, wx, wy, wz]
            use_nullspace_optimization: 是否使用零空间优化

        Returns:
            (q_next, info): 下一步关节角度和求解信息
        """
        # 前向运动学计算当前末端执行器位姿
        pin.forwardKinematics(self.model, self.data, q_current)
        pin.updateFramePlacements(self.model, self.data)

        current_pose_SE3 = self.data.oMf[self.ee_frame_id]
        current_position = current_pose_SE3.translation
        current_rotation = current_pose_SE3.rotation
        current_quaternion = pin.Quaternion(current_rotation).coeffs()  # [x, y, z, w]

        # 计算位置误差
        target_position = target_pose[:3]
        target_quaternion = target_pose[3:]  # [qx, qy, qz, qw]

        position_error = target_position - current_position

        # 计算姿态误差（四元数误差转换为角速度误差）
        current_quat = pin.Quaternion(current_quaternion[3], current_quaternion[0],
                                    current_quaternion[1], current_quaternion[2])
        target_quat = pin.Quaternion(target_quaternion[3], target_quaternion[0],
                                   target_quaternion[1], target_quaternion[2])

        # 姿态误差：ω = 2 * (q_target * q_current^-1).vec
        quat_error = target_quat * current_quat.inverse()
        orientation_error = 2.0 * quat_error.vec

        # 组合位姿误差
        pose_error = np.concatenate([position_error, orientation_error])

        # 计算目标末端执行器速度
        if target_velocity is None:
            # 比例控制：v = Kp * error
            Kp_pos = 5.0  # 位置增益
            Kp_ori = 3.0  # 姿态增益
            target_ee_velocity = np.concatenate([
                Kp_pos * position_error,
                Kp_ori * orientation_error
            ])
        else:
            # 使用给定的目标速度加上误差补偿
            Kp_pos = 2.0
            Kp_ori = 1.0
            target_ee_velocity = target_velocity + np.concatenate([
                Kp_pos * position_error,
                Kp_ori * orientation_error
            ])

        # 计算雅可比矩阵
        J = self.compute_jacobian(q_current)

        # 检查可操作性
        manipulability = self.compute_manipulability(J)

        # 计算阻尼伪逆
        if manipulability < self.manipulability_threshold:
            warnings.warn(f"接近奇异点！可操作性: {manipulability:.6f}")
            J_pinv = self.damped_least_squares_inverse(J)
        else:
            J_pinv = self.damped_least_squares_inverse(J, lambda_damping=0.0)

        # 主任务：末端执行器跟踪
        q_dot_primary = J_pinv @ target_ee_velocity

        # 零空间优化（次要任务）
        if use_nullspace_optimization and self.nv > 6:  # 冗余机械臂
            N = self.compute_nullspace_projector(J, J_pinv)
            q_dot_secondary = self.secondary_task_velocity(q_current)
            q_dot_nullspace = N @ q_dot_secondary
        else:
            q_dot_nullspace = np.zeros(self.nv)

        # 总关节速度
        q_dot_total = q_dot_primary + q_dot_nullspace

        # 关节速度限制
        q_dot_total = np.clip(q_dot_total, -self.max_joint_velocity, self.max_joint_velocity)

        # 数值积分得到下一步关节角度
        q_next = q_current + q_dot_total * self.dt

        # 关节限位约束
        q_next = np.clip(q_next, self.model.lowerPositionLimit, self.model.upperPositionLimit)

        # 返回求解信息
        info = {
            'pose_error': pose_error,
            'position_error_norm': np.linalg.norm(position_error),
            'orientation_error_norm': np.linalg.norm(orientation_error),
            'manipulability': manipulability,
            'joint_velocity': q_dot_total,
            'joint_velocity_norm': np.linalg.norm(q_dot_total),
            'damping_used': manipulability < self.manipulability_threshold,
            'converged': np.linalg.norm(pose_error) < 1e-3
        }

        # 更新历史数据
        self.prev_q = q_current.copy()
        self.prev_error = pose_error.copy()

        return q_next, info

    def solve_trajectory(self,
                        q_start: np.ndarray,
                        target_poses: np.ndarray,
                        max_iterations: int = 1000) -> Tuple[np.ndarray, list]:
        """
        求解轨迹跟踪

        Args:
            q_start: 起始关节角度
            target_poses: 目标位姿序列 [N x 7]
            max_iterations: 每个目标点的最大迭代次数

        Returns:
            (q_trajectory, info_list): 关节轨迹和求解信息列表
        """
        n_waypoints = target_poses.shape[0]
        q_trajectory = np.zeros((n_waypoints, self.nq))
        info_list = []

        q_current = q_start.copy()

        for i, target_pose in enumerate(target_poses):
            # 对每个目标点进行迭代求解
            for iteration in range(max_iterations):
                q_next, info = self.solve_ik(q_current, target_pose)
                q_current = q_next

                # 检查收敛
                if info['converged']:
                    break

                if iteration == max_iterations - 1:
                    warnings.warn(f"轨迹点 {i} 未收敛，误差: {info['position_error_norm']:.6f}")

            q_trajectory[i] = q_current
            info_list.append(info)

        return q_trajectory, info_list
